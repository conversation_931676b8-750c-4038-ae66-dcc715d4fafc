package com.kevinnovation.detoxlauncher.data

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.geometry.Offset
import com.kevinnovation.detoxlauncher.ui.theme.ClockColors

/**
 * Enum for gradient directions
 */
enum class GradientDirection {
    HORIZONTAL,
    VERTICAL,
    DIAGONAL_TL_BR, // Top-left to bottom-right
    DIAGONAL_TR_BL, // Top-right to bottom-left
    RADIAL
}

/**
 * Data class representing a color theme for the clock
 */
data class ColorTheme(
    val id: String,
    val name: String,
    val primaryColor: Color,
    val secondaryColor: Color,
    val tertiaryColor: Color? = null,
    val gradientDirection: GradientDirection = GradientDirection.HORIZONTAL,
    val isAnimated: Boolean = false,
    val category: ThemeCategory = ThemeCategory.CUSTOM
)

/**
 * Categories for organizing themes
 */
enum class ThemeCategory {
    POPULAR,
    NATURE,
    NEON,
    PASTEL,
    MONOCHROME,
    SUNSET,
    OCEAN,
    FIRE,
    ICE,
    GALAXY,
    AUTUMN,
    CUSTOM,
    FAVORITES
}

/**
 * Data class for storing user's favorite color combinations
 */
data class FavoriteColorCombination(
    val id: String,
    val name: String,
    val primaryColor: Color,
    val secondaryColor: Color,
    val tertiaryColor: Color? = null,
    val gradientDirection: GradientDirection = GradientDirection.HORIZONTAL,
    val dateCreated: Long = System.currentTimeMillis()
)

/**
 * Data class for accessibility settings
 */
data class AccessibilitySettings(
    val highContrast: Boolean = false,
    val colorBlindFriendly: Boolean = false,
    val minimumContrastRatio: Float = 4.5f,
    val autoAdjustBrightness: Boolean = false
)

/**
 * Extension functions for creating gradients
 */
fun ColorTheme.createBrush(width: Float = 1000f, height: Float = 1000f): Brush {
    val colors = if (tertiaryColor != null) {
        listOf(primaryColor, secondaryColor, tertiaryColor)
    } else {
        listOf(primaryColor, secondaryColor)
    }
    
    return when (gradientDirection) {
        GradientDirection.HORIZONTAL -> Brush.horizontalGradient(colors)
        GradientDirection.VERTICAL -> Brush.verticalGradient(colors)
        GradientDirection.DIAGONAL_TL_BR -> Brush.linearGradient(
            colors = colors,
            start = Offset(0f, 0f),
            end = Offset(width, height)
        )
        GradientDirection.DIAGONAL_TR_BL -> Brush.linearGradient(
            colors = colors,
            start = Offset(width, 0f),
            end = Offset(0f, height)
        )
        GradientDirection.RADIAL -> Brush.radialGradient(
            colors = colors,
            center = Offset(width / 2, height / 2),
            radius = minOf(width, height) / 2
        )
    }
}

/**
 * Utility functions for color manipulation
 */
object ColorUtils {
    /**
     * Calculate contrast ratio between two colors
     */
    fun calculateContrastRatio(color1: Color, color2: Color): Float {
        val luminance1 = calculateLuminance(color1)
        val luminance2 = calculateLuminance(color2)
        
        val lighter = maxOf(luminance1, luminance2)
        val darker = minOf(luminance1, luminance2)
        
        return (lighter + 0.05f) / (darker + 0.05f)
    }
    
    /**
     * Calculate relative luminance of a color
     */
    private fun calculateLuminance(color: Color): Float {
        val r = if (color.red <= 0.03928f) color.red / 12.92f else kotlin.math.pow((color.red + 0.055f) / 1.055f, 2.4).toFloat()
        val g = if (color.green <= 0.03928f) color.green / 12.92f else kotlin.math.pow((color.green + 0.055f) / 1.055f, 2.4).toFloat()
        val b = if (color.blue <= 0.03928f) color.blue / 12.92f else kotlin.math.pow((color.blue + 0.055f) / 1.055f, 2.4).toFloat()

        return 0.2126f * r + 0.7152f * g + 0.0722f * b
    }
    
    /**
     * Check if color combination is accessible
     */
    fun isAccessible(color1: Color, color2: Color, minimumRatio: Float = 4.5f): Boolean {
        return calculateContrastRatio(color1, color2) >= minimumRatio
    }
    
    /**
     * Generate complementary color
     */
    fun getComplementaryColor(color: Color): Color {
        return Color(
            red = 1f - color.red,
            green = 1f - color.green,
            blue = 1f - color.blue,
            alpha = color.alpha
        )
    }
    
    /**
     * Generate analogous colors
     */
    fun getAnalogousColors(color: Color): List<Color> {
        // This is a simplified implementation
        // In a real app, you'd convert to HSV and adjust hue
        return listOf(
            color.copy(red = minOf(1f, color.red + 0.1f)),
            color,
            color.copy(blue = minOf(1f, color.blue + 0.1f))
        )
    }
    
    /**
     * Adjust color brightness
     */
    fun adjustBrightness(color: Color, factor: Float): Color {
        return Color(
            red = (color.red * factor).coerceIn(0f, 1f),
            green = (color.green * factor).coerceIn(0f, 1f),
            blue = (color.blue * factor).coerceIn(0f, 1f),
            alpha = color.alpha
        )
    }
}

/**
 * Time-based color suggestions
 */
object TimeBasedColors {
    fun getSuggestedThemeForTime(hour: Int): ColorTheme {
        return when (hour) {
            in 6..11 -> ColorPresets.morningTheme
            in 12..17 -> ColorPresets.afternoonTheme
            in 18..21 -> ColorPresets.eveningTheme
            else -> ColorPresets.nightTheme
        }
    }
}
